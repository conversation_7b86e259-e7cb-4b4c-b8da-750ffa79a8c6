package ai

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"go-api-solve/internal/model"
)

// QwenClient Qwen API客户端
type QwenClient struct {
	APIKey  string
	BaseURL string
	Client  *http.Client
}

// NewQwenClient 创建新的Qwen客户端
func NewQwenClient(apiKey string) *QwenClient {
	return &QwenClient{
		APIKey:  apiKey,
		BaseURL: "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
		Client: &http.Client{
			Timeout: 60 * time.Second,
		},
	}
}

// CallQwenVLPlus 调用Qwen-VL-Plus模型
func (c *QwenClient) CallQwenVLPlus(ctx context.Context, imageURL string, config *model.ModelConfig) (*model.QwenResponse, error) {
	// 使用DashScope原生格式构建请求体
	requestBody := map[string]interface{}{
		"model": "qwen-vl-plus",
		"input": map[string]interface{}{
			"messages": []map[string]interface{}{
				{
					"role":    "system",
					"content": []map[string]interface{}{
						{
							"text": config.RoleSystem,
						},
					},
				},
				{
					"role": "user",
					"content": []map[string]interface{}{
						{
							"image": imageURL,
						},
						{
							"text": config.RoleUser,
						},
					},
				},
			},
		},
		"parameters": map[string]interface{}{
			"temperature":        config.Temperature,
			"top_p":              config.TopP,
			"top_k":              config.TopK,
			"repetition_penalty": config.RepetitionPenalty,
			"presence_penalty":   config.PresencePenalty,
			"response_format":    map[string]string{"type": config.ResponseFormat},
		},
	}

	// 序列化请求体
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", c.BaseURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Authorization", "Bearer "+c.APIKey)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := c.Client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// 解析响应
	var qwenResponse model.QwenResponse
	if err := json.Unmarshal(bodyBytes, &qwenResponse); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &qwenResponse, nil
}

// ExtractContentFromQwenResponse 从Qwen响应中提取内容
func ExtractContentFromQwenResponse(response *model.QwenResponse) (string, error) {
	// DashScope原生API主要使用Output.Text字段
	if response.Output.Text != "" {
		return response.Output.Text, nil
	}

	// 检查choices是否存在
	if len(response.Output.Choices) > 0 {
		choice := response.Output.Choices[0]

		// 处理新的数组格式的content
		if len(choice.Message.Content) > 0 {
			// 遍历content数组，查找text字段
			for _, contentItem := range choice.Message.Content {
				if text, ok := contentItem["text"].(string); ok && text != "" {
					return text, nil
				}
			}
		}
	}

	return "", fmt.Errorf("no content found in Qwen response")
}
