package utils

import (
	"encoding/json"
	"fmt"
	"go-api-solve/internal/model"
	"regexp"
	"strings"
)

// FormatQwenData 格式化Qwen返回的数据
// 根据FormatQwenData.md文档的要求进行数据处理
func FormatQwenData(qwenResponse string) (*model.QwenData, error) {
	// 解析JSON响应
	var qwenData model.QwenData
	if err := json.Unmarshal([]byte(qwenResponse), &qwenData); err != nil {
		return nil, fmt.Errorf("failed to parse Qwen response: %w", err)
	}

	// 验证问题类型
	if !isValidQuestionType(qwenData.QuestionType) {
		return nil, fmt.Errorf("图片解析异常，请重新拍摄")
	}

	// 清洗题干
	qwenData.QuestionText = CleanQuestionText(qwenData.QuestionText)

	return &qwenData, nil
}

// isValidQuestionType 验证问题类型是否有效
func isValidQuestionType(questionType string) bool {
	validTypes := []string{"判断题", "多选题", "单选题"}
	
	// 检查是否为空或无效值
	if questionType == "" {
		return false
	}
	
	// 检查是否在有效类型列表中
	for _, validType := range validTypes {
		if questionType == validType {
			return true
		}
	}
	
	return false
}

// CleanQuestionText 清洗题干文本
// 使用正则表达式清除题干开头的序号和标记
func CleanQuestionText(questionText string) string {
	// 添加调试日志
	fmt.Printf("🔍 清洗前: %s\n", questionText)

	// 根据FormatQwenData.md提供的正则表达式，修复序号匹配顺序
	// 将 (?:[1-9]|1[0-9]|20) 改为 (?:20|1[0-9]|[1-9]) 确保长模式优先匹配
	regex := regexp.MustCompile(`^[(（【]?(判断题|单选题|多选题|断题|题)?[)）】]?\s*0?(?:20|1[0-9]|[1-9])[、.．]?\s*`)

	// 查看匹配内容
	matches := regex.FindAllString(questionText, -1)
	if len(matches) > 0 {
		fmt.Printf("🎯 匹配到: %s\n", matches[0])
	} else {
		fmt.Printf("❌ 未匹配到任何内容\n")
	}

	// 清除匹配的前缀
	cleaned := regex.ReplaceAllString(questionText, "")

	// 去除首尾空白字符
	result := strings.TrimSpace(cleaned)

	fmt.Printf("✅ 清洗后: %s\n", result)
	fmt.Println(strings.Repeat("-", 50))

	return result
}

// ConvertQwenToQuestion 将Qwen数据转换为Question模型
func ConvertQwenToQuestion(qwenData *model.QwenData, imageURL string, qwenRaw string) *model.Question {
	question := &model.Question{
		QuestionType: qwenData.QuestionType,
		QuestionText: qwenData.QuestionText,
		ImageURL:     imageURL,
		IsVerified:   0,
	}

	// 设置选项
	if qwenData.A != "" {
		question.OptionA = &qwenData.A
	}
	if qwenData.B != "" {
		question.OptionB = &qwenData.B
	}
	if qwenData.C != "" {
		question.OptionC = &qwenData.C
	}
	if qwenData.D != "" {
		question.OptionD = &qwenData.D
	}
	if qwenData.Y != "" {
		question.OptionY = &qwenData.Y
	}
	if qwenData.N != "" {
		question.OptionN = &qwenData.N
	}

	// 设置原始数据
	var qwenRawData model.JSONField
	if err := json.Unmarshal([]byte(qwenRaw), &qwenRawData); err == nil {
		question.QwenRaw = qwenRawData
	}

	// 设置解析后的数据
	qwenParsedData := make(model.JSONField)
	qwenParsedBytes, _ := json.Marshal(qwenData)
	json.Unmarshal(qwenParsedBytes, &qwenParsedData)
	question.QwenParsed = qwenParsedData

	return question
}

// BuildOptionsMap 构建选项映射
func BuildOptionsMap(question *model.Question) map[string]string {
	options := make(map[string]string)
	
	if question.OptionA != nil {
		options["A"] = *question.OptionA
	}
	if question.OptionB != nil {
		options["B"] = *question.OptionB
	}
	if question.OptionC != nil {
		options["C"] = *question.OptionC
	}
	if question.OptionD != nil {
		options["D"] = *question.OptionD
	}
	if question.OptionY != nil {
		options["Y"] = *question.OptionY
	}
	if question.OptionN != nil {
		options["N"] = *question.OptionN
	}
	
	return options
}

// ConvertToQuestionResponse 将Question模型转换为响应格式
func ConvertToQuestionResponse(question *model.Question) *model.QuestionResponse {
	response := &model.QuestionResponse{
		QuestionType: question.QuestionType,
		QuestionText: question.QuestionText,
		Options:      BuildOptionsMap(question),
		ImageURL:     question.ImageURL,
		IsVerified:   fmt.Sprintf("%d", question.IsVerified),
	}

	// 设置用户图片名称
	if question.UserImage != nil {
		response.UserImage = *question.UserImage
	}

	// 设置解析
	if question.Analysis != nil {
		response.Analysis = *question.Analysis
	}

	// 设置答案
	if question.Answer != nil {
		answerMap := make(map[string]string)
		for key, value := range question.Answer {
			if strValue, ok := value.(string); ok {
				answerMap[key] = strValue
			}
		}
		response.Answer = answerMap
	}

	return response
}
